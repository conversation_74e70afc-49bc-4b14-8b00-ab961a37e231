/**
 * Simple Logger Utility
 * 
 * A lightweight logging utility that provides structured logging
 * with different log levels and console output formatting.
 */

class Logger {
  constructor() {
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.currentLevel = process.env.LOG_LEVEL || 'info';
  }

  /**
   * Check if a log level should be output
   */
  shouldLog(level) {
    return this.levels[level] <= this.levels[this.currentLevel];
  }

  /**
   * Format log message with timestamp and level
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const levelUpper = level.toUpperCase().padEnd(5);
    
    let logMessage = `[${timestamp}] ${levelUpper} ${message}`;
    
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  }

  /**
   * Log error messages
   */
  error(meta, message) {
    if (!this.shouldLog('error')) return;
    
    if (typeof meta === 'string') {
      message = meta;
      meta = {};
    }
    
    const formatted = this.formatMessage('error', message, meta);
    console.error(formatted);
  }

  /**
   * Log warning messages
   */
  warn(meta, message) {
    if (!this.shouldLog('warn')) return;
    
    if (typeof meta === 'string') {
      message = meta;
      meta = {};
    }
    
    const formatted = this.formatMessage('warn', message, meta);
    console.warn(formatted);
  }

  /**
   * Log info messages
   */
  info(meta, message) {
    if (!this.shouldLog('info')) return;
    
    if (typeof meta === 'string') {
      message = meta;
      meta = {};
    }
    
    const formatted = this.formatMessage('info', message, meta);
    console.log(formatted);
  }

  /**
   * Log debug messages
   */
  debug(meta, message) {
    if (!this.shouldLog('debug')) return;
    
    if (typeof meta === 'string') {
      message = meta;
      meta = {};
    }
    
    const formatted = this.formatMessage('debug', message, meta);
    console.log(formatted);
  }

  /**
   * Set log level
   */
  setLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.currentLevel = level;
    }
  }
}

// Export singleton instance
const logger = new Logger();
export default logger;
