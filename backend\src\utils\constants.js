const Role = {
  USER: 'user',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
  GUEST: 'guest',
  MODERATOR: 'moderator',
  CONTRIBUTOR: 'contributor',
  SUPPORT: 'support',
  MANAGER: 'manager',
  DEVELOPER: 'developer'
};


const BillingCycle = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  ONE_TIME: 'one_time',
};

const SubscriptionStatus = {
  ACTIVE: 'active',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
};
const PlanType = {
  FREE: 'free',
  PAID: 'paid',
};
const TransactionType = {
  PURCHASE: 'purchase',
  REFUND: 'refund',
  CREDIT: 'credit',
  DEBIT: 'debit',
};

const FeatureType = {
  USER: 'user',
  PROJECT: 'project',
  STORAGE_GB: 'storage_gb',
  API_CALLS: 'api_calls',
};

const FeatureQuota = {
  MAX_USERS: 'max_users',
  MAX_PROJECTS: 'max_projects',
  MAX_STORAGE_GB: 'max_storage_gb',
  MAX_API_CALLS: 'max_api_calls',
};

const FeatureQuotaDefaults = {
  [FeatureQuota.MAX_USERS]: 1,
  [FeatureQuota.MAX_PROJECTS]: 5,
  [FeatureQuota.MAX_STORAGE_GB]: 1,
  [FeatureQuota.MAX_API_CALLS]: 1000,
};

const CreditTransactionType = {
  PURCHASE: 'PURCHASE',
  USAGE_EMBEDDING_CREATION: 'USAGE_EMBEDDING_CREATION',
  USAGE_ADVANCED_SEARCH: 'USAGE_ADVANCED_SEARCH',
  REFUND: 'REFUND',
  ADMIN_GRANT: 'ADMIN_GRANT',
  INITIAL_GRANT: 'INITIAL_GRANT',
};
const CreditTransactionStatus = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
};

const InvoiceStatus = {
  PENDING: 'pending',
  PAID: 'paid',
  OVERDUE: 'overdue',
  CANCELLED: 'cancelled',
};

const ReportStatus = {
  PENDING: 'pending',
  RESOLVED: 'resolved',
  REJECTED: 'rejected',
  ESCALATED: 'escalated',
};

const NotificationType = {
  LOW_CREDIT_BALANCE: 'LOW_CREDIT_BALANCE',
  LOW_FEATURE_QUOTA: 'LOW_FEATURE_QUOTA',
  SUBSCRIPTION_RENEWAL_REMINDER: 'SUBSCRIPTION_RENEWAL_REMINDER',
};



export  {
  Role,
  BillingCycle,
  SubscriptionStatus,
  PlanType,
  TransactionType,
  FeatureType,
  FeatureQuota,
  FeatureQuotaDefaults,
  CreditTransactionType,
  CreditTransactionStatus,
  InvoiceStatus,
  ReportStatus,
  NotificationType
};


