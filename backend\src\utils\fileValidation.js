/**
 * File Validation Utilities
 * 
 * Comprehensive file validation functions for secure file upload handling.
 * Includes MIME type validation, filename security checks, and file size validation.
 */

import path from 'path';

/**
 * Allowed MIME types for image uploads
 */
export const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
  'image/tiff',
  'image/tif'
];

/**
 * Allowed file extensions
 */
export const ALLOWED_EXTENSIONS = [
  '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'
];

/**
 * File size limits
 */
export const FILE_SIZE_LIMITS = {
  MIN_SIZE: 100, // 100 bytes
  MAX_SIZE: 100 * 1024 * 1024, // 100MB
};

/**
 * Malicious filename patterns to detect
 */
export const MALICIOUS_PATTERNS = [
  /\.\./,           // Directory traversal
  /[<>:"|?*]/,      // Invalid filename characters
  /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i, // Windows reserved names
  /^\./,            // Hidden files
  /\.(exe|bat|cmd|scr|pif|com|dll|vbs|js|jar|php|asp|jsp|py|rb|pl)$/i // Executable extensions
];

/**
 * Validate file MIME type
 */
export function validateMimeType(mimetype) {
  if (!mimetype) {
    return { valid: false, error: 'MIME type is required' };
  }
  
  if (!ALLOWED_MIME_TYPES.includes(mimetype.toLowerCase())) {
    return { 
      valid: false, 
      error: `Invalid file type. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}` 
    };
  }
  
  return { valid: true };
}

/**
 * Validate file extension
 */
export function validateFileExtension(filename) {
  if (!filename) {
    return { valid: false, error: 'Filename is required' };
  }
  
  const ext = path.extname(filename).toLowerCase();
  
  if (!ALLOWED_EXTENSIONS.includes(ext)) {
    return { 
      valid: false, 
      error: `Invalid file extension. Allowed extensions: ${ALLOWED_EXTENSIONS.join(', ')}` 
    };
  }
  
  return { valid: true };
}

/**
 * Validate file size
 */
export function validateFileSize(size) {
  if (typeof size !== 'number' || size < 0) {
    return { valid: false, error: 'Invalid file size' };
  }
  
  if (size < FILE_SIZE_LIMITS.MIN_SIZE) {
    return { 
      valid: false, 
      error: `File too small. Minimum size: ${FILE_SIZE_LIMITS.MIN_SIZE} bytes` 
    };
  }
  
  if (size > FILE_SIZE_LIMITS.MAX_SIZE) {
    return { 
      valid: false, 
      error: `File too large. Maximum size: ${Math.round(FILE_SIZE_LIMITS.MAX_SIZE / (1024 * 1024))}MB` 
    };
  }
  
  return { valid: true };
}

/**
 * Validate filename for security issues
 */
export function validateFilename(filename) {
  if (!filename || typeof filename !== 'string') {
    return { valid: false, error: 'Valid filename is required' };
  }
  
  // Check for malicious patterns
  for (const pattern of MALICIOUS_PATTERNS) {
    if (pattern.test(filename)) {
      return { 
        valid: false, 
        error: 'Filename contains invalid or potentially dangerous characters' 
      };
    }
  }
  
  // Check filename length
  if (filename.length > 255) {
    return { valid: false, error: 'Filename too long (max 255 characters)' };
  }
  
  // Check for null bytes
  if (filename.includes('\0')) {
    return { valid: false, error: 'Filename contains null bytes' };
  }
  
  return { valid: true };
}

/**
 * Comprehensive file validation
 */
export function validateFile(file) {
  if (!file) {
    return { valid: false, error: 'File is required' };
  }
  
  // Validate MIME type
  const mimeValidation = validateMimeType(file.mimetype);
  if (!mimeValidation.valid) {
    return mimeValidation;
  }
  
  // Validate file extension
  const extValidation = validateFileExtension(file.originalname);
  if (!extValidation.valid) {
    return extValidation;
  }
  
  // Validate file size
  const sizeValidation = validateFileSize(file.size);
  if (!sizeValidation.valid) {
    return sizeValidation;
  }
  
  // Validate filename
  const filenameValidation = validateFilename(file.originalname);
  if (!filenameValidation.valid) {
    return filenameValidation;
  }
  
  return { valid: true };
}

/**
 * Generate safe filename
 */
export function generateSafeFilename(originalName) {
  const ext = path.extname(originalName).toLowerCase();
  const baseName = path.basename(originalName, ext);
  
  // Remove unsafe characters and replace with underscores
  const safeName = baseName
    .replace(/[^a-zA-Z0-9\-_]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .substring(0, 50); // Limit length
  
  // Add timestamp to ensure uniqueness
  const timestamp = Date.now();
  
  return `${safeName}_${timestamp}${ext}`;
}

/**
 * Validate image dimensions parameters
 */
export function validateDimensions(width, height) {
  const errors = [];
  
  if (width !== undefined) {
    const w = parseInt(width);
    if (isNaN(w) || w < 1 || w > 8000) {
      errors.push('Width must be between 1 and 8000 pixels');
    }
  }
  
  if (height !== undefined) {
    const h = parseInt(height);
    if (isNaN(h) || h < 1 || h > 8000) {
      errors.push('Height must be between 1 and 8000 pixels');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate quality parameter
 */
export function validateQuality(quality) {
  if (quality === undefined) {
    return { valid: true }; // Quality is optional
  }

  const q = parseInt(quality);
  if (isNaN(q) || q < 1 || q > 100) {
    return {
      valid: false,
      error: 'Quality must be between 1 and 100'
    };
  }

  return { valid: true };
}

/**
 * Assert that a file upload is valid (throws error if not)
 * This is a wrapper around validateFile that throws instead of returning validation result
 */
export function assertValidUpload(file) {
  const validation = validateFile(file);
  if (!validation.valid) {
    const error = new Error(validation.error || 'File validation failed');
    error.statusCode = 400;
    throw error;
  }
}
