import { createClient } from '@supabase/supabase-js';
import config from './index.js';

/**
 * Supabase Client Configuration
 * 
 * This module provides a configured Supabase client for direct database operations,
 * real-time subscriptions, and other Supabase-specific features.
 * 
 * Note: We're using Prisma as our primary ORM, but this client can be used for:
 * - Real-time subscriptions
 * - File storage operations
 * - Direct SQL queries when needed
 * - Supabase-specific features
 */

class SupabaseConfig {
  constructor() {
    this.client = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Supabase client
   */
  initialize() {
    if (this.isInitialized) {
      return this.client;
    }

    const supabaseUrl = config.get('SUPABASE_URL');
    const supabaseKey = config.get('SUPABASE_KEY');

    if (!supabaseUrl || !supabaseKey) {
      console.warn('⚠️  Supabase URL or Key not configured. Some features may not work.');
      return null;
    }

    try {
      this.client = createClient(supabaseUrl, supabase<PERSON>ey, {
        auth: {
          // Since we're using fake auth mode, disable Supabase auth
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
        global: {
          headers: {
            'X-Client-Info': 'saassy-backend',
          },
        },
      });

      this.isInitialized = true;
      console.log('✅ Supabase client initialized successfully');
      return this.client;
    } catch (error) {
      console.error('❌ Failed to initialize Supabase client:', error);
      return null;
    }
  }

  /**
   * Get the Supabase client instance
   */
  getClient() {
    if (!this.isInitialized) {
      return this.initialize();
    }
    return this.client;
  }

  /**
   * Test database connection
   */
  async testConnection() {
    const client = this.getClient();
    if (!client) {
      return { success: false, error: 'Supabase client not initialized' };
    }

    try {
      // Simple query to test connection
      const { data, error } = await client
        .from('User')
        .select('count')
        .limit(1);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, message: 'Supabase connection successful' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats() {
    const client = this.getClient();
    if (!client) {
      return null;
    }

    try {
      const stats = {};

      // Get user count
      const { count: userCount } = await client
        .from('User')
        .select('*', { count: 'exact', head: true });
      stats.users = userCount || 0;

      // Get product count
      const { count: productCount } = await client
        .from('Product')
        .select('*', { count: 'exact', head: true });
      stats.products = productCount || 0;

      // Get purchase count
      const { count: purchaseCount } = await client
        .from('ProductPurchase')
        .select('*', { count: 'exact', head: true });
      stats.purchases = purchaseCount || 0;

      return stats;
    } catch (error) {
      console.error('Error getting database stats:', error);
      return null;
    }
  }

  /**
   * Enable real-time for a table
   */
  subscribeToTable(tableName, callback, filter = '*') {
    const client = this.getClient();
    if (!client) {
      console.error('Cannot subscribe: Supabase client not initialized');
      return null;
    }

    return client
      .channel(`public:${tableName}`)
      .on('postgres_changes', 
        { 
          event: filter, 
          schema: 'public', 
          table: tableName 
        }, 
        callback
      )
      .subscribe();
  }

  /**
   * Cleanup and close connections
   */
  async cleanup() {
    if (this.client) {
      // Remove all subscriptions
      this.client.removeAllChannels();
      this.isInitialized = false;
      this.client = null;
      console.log('🧹 Supabase client cleaned up');
    }
  }
}

// Export singleton instance
const supabaseConfig = new SupabaseConfig();
export default supabaseConfig;

// Export the client getter for convenience
export const getSupabaseClient = () => supabaseConfig.getClient();
