//const { PrismaClient } = require('@prisma/client'); // Ensure you have @prisma/client installed
import { PrismaClient, CreditTransactionType } from '@prisma/client'; // Import Prisma for type safety

//const { PlanType, CreditTransactionType } = require('../utils/constants.js'); // Ensure these constants are defined in your utils/constants.js
import  { PlanType } from '../utils/constants.js'; // Import constants for plan types
//const bcrypt = require('bcrypt');
import bcrypt from 'bcrypt'; // Import bcrypt for password hashing
//const jwt = require('jsonwebtoken');
import jwt from 'jsonwebtoken'; // Import JWT for token generation
//const crypto = require('crypto');
import crypto from 'crypto'; // Import crypto for token generation


/*const {
  ConflictError,
  UnauthorizedError,
  BadRequestError,
  InternalServerError,
} = require('../utils/errors.js'); 
*/
import {
  ConflictError,
  UnauthorizedError,
  BadRequestError,
  InternalServerError,
} from '../utils/errors.js'; // Import custom error classes




// const {
//   JWT_SECRET,
//   JWT_REFRESH_SECRET,
//   JWT_EXPIRES_IN,
//   JWT_REFRESH_EXPIRES_IN,
// } = require('../config/jwtConfig.js');
import {
  JWT_SECRET,
  JWT_REFRESH_SECRET,
  JWT_EXPIRES_IN,
  JWT_REFRESH_EXPIRES_IN,
} from '../config/jwtConfig.js'; // Import JWT configuration


//const { FRONTEND_URL } = require('../config/appConfig.js');
import { FRONTEND_URL } from '../config/appConfig.js';

//const { validatePassword } = require('../utils/validation.js');
import { validatePassword } from '../utils/validation.js'; // Import password validation utility


const prisma = new PrismaClient();
const SALT_ROUNDS = 10;
const PASSWORD_RESET_TOKEN_EXPIRES_IN_HOURS = 1;

const registerUser = async ({ email, password, name }) => {
  const existingUser = await prisma.user.findUnique({ where: { email } });
  if (existingUser) {
    throw new ConflictError('User with this email already exists.');
  }

  const passwordHash = await bcrypt.hash(password, SALT_ROUNDS);
  const verificationToken = crypto.randomBytes(32).toString('hex');

  // Use a transaction to create user and assign freemium benefits
  const newUserAndBenefits = await prisma.$transaction(async (tx) => {
    const newUser = await tx.user.create({
      data: {
        email,
        passwordHash,
        name: name || null,
        role: 'CUSTOMER',
        isEmailVerified: false,
        emailVerificationToken: verificationToken,
        currentSubscriptionId: null,
        // currentSubscriptionId will be null by default
      },
    });

    // Find an active Freemium plan

    tx.plan = tx.plan || prisma.plan; // Ensure tx.plan is available in the transaction context
    tx.userCredit = tx.userCredit || prisma.userCredit; // Ensure tx.userCredit is available in the transaction context
    tx.creditLog = tx.creditLog || prisma.creditLog; // Ensure tx.creditLog is available in the transaction context
    tx.usageService = tx.usageService || prisma.usageService; // Ensure tx.usageService is available in the transaction context
    tx.subscription = tx.subscription || prisma.subscription; // Ensure tx.subscription is available in the transaction context
    tx.featureQuota = tx.featureQuota || prisma.featureQuota; // Ensure tx.featureQuota is available in the transaction context

    const freemiumPlan = await tx.plan.findFirst({
      where: {
        planType: PlanType.FREEMIUM,
        isActive: true,
      },
      orderBy: [
        // In case there are multiple, pick one consistently (e.g., oldest)
        { createdAt: 'asc' },
      ],
    });
    console.log(
      `Freemium plan found: ${freemiumPlan ? freemiumPlan.name : 'None'}`
    );

    if (freemiumPlan) {
      console.log(
        `Found active Freemium plan: ${freemiumPlan.name} for new user ${newUser.email}`
      );
      if (freemiumPlan.creditsGranted && freemiumPlan.creditsGranted > 0) {
        // Grant initial credits
        await tx.userCredit.upsert({
          where: { userId: newUser.id },
          update: { balance: { increment: freemiumPlan.creditsGranted } }, // Should not happen for new user
          create: { userId: newUser.id, balance: freemiumPlan.creditsGranted },
        });

        console.log('CreditTransactionType:', CreditTransactionType);
        console.log('CreditTransactionType.INITIAL_GRANT:', CreditTransactionType.INITIAL_GRANT);

        await tx.creditLog.create({
          data: {
            userId: newUser.id,
            transactionType: CreditTransactionType.INITIAL_GRANT,
            amount: freemiumPlan.creditsGranted,
            description: `Initial credits from Freemium plan (${freemiumPlan.name}).`,
          },
        });
        console.log(
          `Granted ${freemiumPlan.creditsGranted} initial credits to user ${newUser.email} from Freemium plan.`
        );
      } else {
        console.log(
          `Freemium plan ${freemiumPlan.name} has no initial credits to grant or invalid amount.`
        );
      }
      // Note: Freemium plan's featureQuotas are not directly assigned via a Subscription record here.
      // As per subtask Option B, enforcement relies on usageService deducting from these initial credits,
      // or if featureQuotas are to be used without a subscription, usageService would need modification
      // to look up a default Freemium plan if no active subscription exists.
    } else {
      console.warn(
        `No active Freemium plan found for new user assignment. User ${newUser.email} will start with no plan benefits.`
      );
    }

    return newUser;
  });

  const verificationLink = `${FRONTEND_URL}/verify-email/${verificationToken}`;
  // In a real app, you would send an email here.
  console.log(`Email verification link for ${email}: ${verificationLink}`);

  // Exclude sensitive fields from the returned user object
  const {
    passwordHash: _,
    emailVerificationToken: __,
    ...userWithoutPassword
  } = newUserAndBenefits;
  return userWithoutPassword;
};

const loginUser = async ({ email, password }) => {
  if (!email || !password) {
    throw new BadRequestError('Email and password are required.');
  }

  const user = await prisma.user.findUnique({ where: { email } });
  if (!user) {
    throw new UnauthorizedError('Invalid email or password.');
  }

  if (!user.isEmailVerified) {
    // Consider if this check should be here or if the frontend should guide the user.
    // For now, strict check.
    throw new UnauthorizedError(
      'Please verify your email address before logging in.'
    );
  }

  const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
  if (!isPasswordValid) {
    throw new UnauthorizedError('Invalid email or password.');
  }

  const accessTokenPayload = { userId: user.id, role: user.role };
  const accessToken = jwt.sign(accessTokenPayload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
  const refreshToken = jwt.sign({ userId: user.id }, JWT_REFRESH_SECRET, {
    expiresIn: JWT_REFRESH_EXPIRES_IN,
  });

  // Exclude sensitive fields
  const {
    passwordHash,
    emailVerificationToken,
    isEmailVerified,
    ...userWithoutPassword
  } = user;
  return { user: userWithoutPassword, accessToken, refreshToken };
};

const requestPasswordReset = async (email) => {
  if (!email) {
    throw new BadRequestError('Email is required.');
  }
  const user = await prisma.user.findUnique({ where: { email } });

  if (user) {
    // Additional check: disallow password reset if email is not verified?
    // if (!user.isEmailVerified) {
    //   console.log(`Password reset requested for unverified email: ${email}`);
    //   // Still return generic message to prevent email enumeration
    //   return { message: 'If your email address exists in our system and is verified, you will receive a password reset link.' };
    // }

    const resetToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(
      expiresAt.getHours() + PASSWORD_RESET_TOKEN_EXPIRES_IN_HOURS
    );

    await prisma.user.update({
      where: { email },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpiresAt: expiresAt,
      },
    });

    const resetLink = `${FRONTEND_URL}/reset-password/${resetToken}`;
    // In a real app, send email
    console.log(
      `Password reset requested for ${email}. Token: ${resetToken}, Link: ${resetLink}`
    );
  } else {
    // Log this, but don't reveal to the client if the email exists or not
    console.log(`Password reset requested for non-existent email: ${email}`);
  }
  // Generic message to prevent email enumeration
  return {
    message:
      'If your email address exists in our system, you will receive a password reset link.',
  };
};

const resetPassword = async (token, newPassword) => {
  if (!token || !newPassword) {
    throw new BadRequestError('Token and new password are required.');
  }

  const passwordValidation = validatePassword(newPassword);
  if (!passwordValidation.valid) {
    throw new BadRequestError(
      passwordValidation.message || 'Password does not meet requirements.'
    );
  }

  const user = await prisma.user.findUnique({
    where: { passwordResetToken: token },
  });

  if (!user) {
    throw new BadRequestError('Invalid or expired password reset token.');
  }

  if (user.passwordResetExpiresAt < new Date()) {
    // Clean up expired token
    await prisma.user.update({
      where: { id: user.id },
      data: { passwordResetToken: null, passwordResetExpiresAt: null },
    });
    throw new BadRequestError('Invalid or expired password reset token.');
  }

  const newPasswordHash = await bcrypt.hash(newPassword, SALT_ROUNDS);

  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordHash: newPasswordHash,
      passwordResetToken: null, // Clear the token
      passwordResetExpiresAt: null,
    },
  });

  return { message: 'Password has been reset successfully.' };
};

const verifyEmail = async (token) => {
  if (!token) {
    throw new BadRequestError('Verification token is required.');
  }

  const user = await prisma.user.findUnique({
    where: { emailVerificationToken: token },
  });

  if (!user) {
    // To prevent token reuse or probing, we could invalidate the token after one use attempt
    // or if it's simply not found. For now, just error.
    throw new BadRequestError('Invalid or expired email verification token.');
  }

  // Potentially add a check here if user.isEmailVerified is already true.

  await prisma.user.update({
    where: { id: user.id },
    data: {
      isEmailVerified: true,
      emailVerificationToken: null, // Clear the token once used
    },
  });

  return { message: 'Email verified successfully.' };
};

async function refreshAccessToken(tokenString) {
  if (!tokenString) {
    throw new BadRequestError('Refresh token is required.');
  }

  try {
    const decoded = jwt.verify(tokenString, JWT_REFRESH_SECRET);
    const userId = decoded.userId;

    if (!userId) {
      throw new UnauthorizedError('Invalid refresh token: User ID missing.');
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedError('Invalid refresh token: User not found.');
    }

    // Optionally, add checks here if user is active, not banned, etc.
    // For example: if (!user.isActive) throw new UnauthorizedError('User is inactive.');
    if (!user.isEmailVerified) {
      // Assuming email verification is a sign of an active/usable account
      throw new UnauthorizedError('User email not verified.');
    }

    const accessTokenPayload = { userId: user.id, role: user.role };
    const newAccessToken = jwt.sign(accessTokenPayload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
    });

    // Not implementing refresh token rotation for this version to align with current login behavior.
    // If rotation was needed, a new refresh token would be generated and returned here.
    return { accessToken: newAccessToken };
  } catch (error) {
    if (
      error instanceof jwt.JsonWebTokenError ||
      error instanceof jwt.TokenExpiredError
    ) {
      throw new UnauthorizedError(
        `Invalid or expired refresh token: ${error.message}`
      );
    }
    // Re-throw other errors (like BadRequestError, UnauthorizedError from above checks, or Prisma errors)
    throw error;
  }
}

export	 default {
  registerUser,
  loginUser,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  refreshAccessToken,
};
