
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  passwordHash: 'passwordHash',
  name: 'name',
  role: 'role',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpiresAt: 'passwordResetExpiresAt',
  isEmailVerified: 'isEmailVerified',
  emailVerificationToken: 'emailVerificationToken',
  currentSubscriptionId: 'currentSubscriptionId',
  totalPoints: 'totalPoints'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  message: 'message',
  link: 'link',
  isRead: 'isRead',
  createdAt: 'createdAt',
  actorId: 'actorId'
};

exports.Prisma.TileConfigScalarFieldEnum = {
  id: 'id',
  type: 'type',
  name: 'name',
  defaultData: 'defaultData',
  defaultPosition: 'defaultPosition',
  isEnabled: 'isEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmbeddingScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  description: 'description'
};

exports.Prisma.PlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  priceCents: 'priceCents',
  billingCycle: 'billingCycle',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  planType: 'planType',
  creditsGranted: 'creditsGranted',
  featureQuotas: 'featureQuotas'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  planId: 'planId',
  status: 'status',
  startedAt: 'startedAt',
  expiresAt: 'expiresAt',
  cancelAtPeriodEnd: 'cancelAtPeriodEnd',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  subscriptionId: 'subscriptionId',
  invoiceNumber: 'invoiceNumber',
  totalAmountCents: 'totalAmountCents',
  taxAmountCents: 'taxAmountCents',
  invoiceDate: 'invoiceDate',
  dueDate: 'dueDate',
  paidAt: 'paidAt',
  pdfPath: 'pdfPath',
  description: 'description',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  planId: 'planId',
  creditPurchaseAmount: 'creditPurchaseAmount'
};

exports.Prisma.UserCreditScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  balance: 'balance',
  updatedAt: 'updatedAt'
};

exports.Prisma.CreditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  transactionType: 'transactionType',
  amount: 'amount',
  description: 'description',
  relatedInvoiceId: 'relatedInvoiceId',
  createdAt: 'createdAt'
};

exports.Prisma.FeatureUsageLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  featureType: 'featureType',
  unitsConsumed: 'unitsConsumed',
  description: 'description',
  createdAt: 'createdAt'
};

exports.Prisma.ForumCategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FollowScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  forumCategoryId: 'forumCategoryId',
  createdAt: 'createdAt'
};

exports.Prisma.BookmarkScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  threadId: 'threadId',
  createdAt: 'createdAt'
};

exports.Prisma.PointLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  points: 'points',
  activityType: 'activityType',
  description: 'description',
  relatedThreadId: 'relatedThreadId',
  relatedPostId: 'relatedPostId',
  createdAt: 'createdAt'
};

exports.Prisma.ForumThreadScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  authorId: 'authorId',
  categoryId: 'categoryId',
  viewCount: 'viewCount',
  isPinned: 'isPinned',
  isLocked: 'isLocked',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  tags: 'tags',
  solutionId: 'solutionId'
};

exports.Prisma.ForumPostScalarFieldEnum = {
  id: 'id',
  content: 'content',
  authorId: 'authorId',
  threadId: 'threadId',
  parentPostId: 'parentPostId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ForumReactionScalarFieldEnum = {
  id: 'id',
  emoji: 'emoji',
  userId: 'userId',
  threadId: 'threadId',
  postId: 'postId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  reason: 'reason',
  category: 'category',
  reporterId: 'reporterId',
  threadId: 'threadId',
  postId: 'postId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resolvedById: 'resolvedById',
  resolvedAt: 'resolvedAt',
  resolutionNotes: 'resolutionNotes'
};

exports.Prisma.ModerationLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  reason: 'reason',
  moderatorId: 'moderatorId',
  targetUserId: 'targetUserId',
  targetThreadId: 'targetThreadId',
  targetPostId: 'targetPostId',
  relatedReportId: 'relatedReportId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SettingScalarFieldEnum = {
  key: 'key',
  value: 'value',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  shortDescription: 'shortDescription',
  category: 'category',
  type: 'type',
  status: 'status',
  basePrice: 'basePrice',
  currency: 'currency',
  billingType: 'billingType',
  features: 'features',
  limits: 'limits',
  imageUrl: 'imageUrl',
  galleryImages: 'galleryImages',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  tags: 'tags',
  isDigital: 'isDigital',
  requiresSetup: 'requiresSetup',
  setupInstructions: 'setupInstructions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductPurchaseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  purchasePrice: 'purchasePrice',
  currency: 'currency',
  status: 'status',
  billingType: 'billingType',
  billingPeriod: 'billingPeriod',
  expiresAt: 'expiresAt',
  usageCount: 'usageCount',
  usageLimit: 'usageLimit',
  lastUsedAt: 'lastUsedAt',
  activatedAt: 'activatedAt',
  activationKey: 'activationKey',
  paymentId: 'paymentId',
  paymentMethod: 'paymentMethod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductUsageLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  purchaseId: 'purchaseId',
  action: 'action',
  inputData: 'inputData',
  outputData: 'outputData',
  inputFileSize: 'inputFileSize',
  outputFileSize: 'outputFileSize',
  inputFormat: 'inputFormat',
  outputFormat: 'outputFormat',
  processingTime: 'processingTime',
  success: 'success',
  errorMessage: 'errorMessage',
  creditsUsed: 'creditsUsed',
  createdAt: 'createdAt'
};

exports.Prisma.ProductReviewScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  rating: 'rating',
  title: 'title',
  content: 'content',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserFileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  originalName: 'originalName',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  width: 'width',
  height: 'height',
  format: 'format',
  isProcessed: 'isProcessed',
  processingStatus: 'processingStatus',
  folder: 'folder',
  tags: 'tags',
  isPublic: 'isPublic',
  shareToken: 'shareToken',
  exifData: 'exifData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProcessedFileScalarFieldEnum = {
  id: 'id',
  originalFileId: 'originalFileId',
  userId: 'userId',
  operation: 'operation',
  parameters: 'parameters',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  width: 'width',
  height: 'height',
  format: 'format',
  processingTime: 'processingTime',
  success: 'success',
  errorMessage: 'errorMessage',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  CUSTOMER: 'CUSTOMER',
  EDITOR: 'EDITOR',
  MODERATOR: 'MODERATOR'
};

exports.BillingCycle = exports.$Enums.BillingCycle = {
  MONTHLY: 'MONTHLY',
  YEARLY: 'YEARLY',
  ONE_TIME: 'ONE_TIME'
};

exports.PlanType = exports.$Enums.PlanType = {
  FREEMIUM: 'FREEMIUM',
  SUBSCRIPTION_TIER_1: 'SUBSCRIPTION_TIER_1',
  SUBSCRIPTION_TIER_2: 'SUBSCRIPTION_TIER_2',
  CREDIT_PACK: 'CREDIT_PACK'
};

exports.SubscriptionStatus = exports.$Enums.SubscriptionStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  CANCELED: 'CANCELED',
  EXPIRED: 'EXPIRED',
  PAST_DUE: 'PAST_DUE'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  DRAFT: 'DRAFT',
  ISSUED: 'ISSUED',
  PAID: 'PAID',
  VOID: 'VOID',
  OVERDUE: 'OVERDUE',
  UNCOLLECTIBLE: 'UNCOLLECTIBLE'
};

exports.CreditTransactionType = exports.$Enums.CreditTransactionType = {
  PURCHASE: 'PURCHASE',
  USAGE_EMBEDDING_CREATION: 'USAGE_EMBEDDING_CREATION',
  USAGE_ADVANCED_SEARCH: 'USAGE_ADVANCED_SEARCH',
  REFUND: 'REFUND',
  ADMIN_GRANT: 'ADMIN_GRANT',
  INITIAL_GRANT: 'INITIAL_GRANT'
};

exports.FeatureType = exports.$Enums.FeatureType = {
  EMBEDDING_CREATION: 'EMBEDDING_CREATION',
  ADVANCED_SEARCH: 'ADVANCED_SEARCH',
  DATA_EXPORT: 'DATA_EXPORT',
  API_CALL: 'API_CALL'
};

exports.PointActivityType = exports.$Enums.PointActivityType = {
  KB_ARTICLE_PUBLISHED: 'KB_ARTICLE_PUBLISHED',
  KB_ARTICLE_HELPFUL_VOTE_RECEIVED: 'KB_ARTICLE_HELPFUL_VOTE_RECEIVED',
  KB_ARTICLE_VOTED_HELPFUL: 'KB_ARTICLE_VOTED_HELPFUL',
  FORUM_THREAD_CREATED: 'FORUM_THREAD_CREATED',
  FORUM_COMMENT_CREATED: 'FORUM_COMMENT_CREATED',
  FORUM_THREAD_SOLUTION_AWARDED: 'FORUM_THREAD_SOLUTION_AWARDED',
  FORUM_THREAD_MARKED_SOLVED: 'FORUM_THREAD_MARKED_SOLVED',
  FORUM_REACTION_RECEIVED: 'FORUM_REACTION_RECEIVED',
  FORUM_GAVE_REACTION: 'FORUM_GAVE_REACTION',
  MANUAL_ADJUSTMENT: 'MANUAL_ADJUSTMENT'
};

exports.ReactionEmoji = exports.$Enums.ReactionEmoji = {
  LIKE: 'LIKE',
  HEART: 'HEART',
  LAUGH: 'LAUGH',
  THINKING: 'THINKING',
  SAD: 'SAD',
  ROCKET: 'ROCKET'
};

exports.ReportCategory = exports.$Enums.ReportCategory = {
  SPAM: 'SPAM',
  INAPPROPRIATE_CONTENT: 'INAPPROPRIATE_CONTENT',
  HATE_SPEECH: 'HATE_SPEECH',
  INFRINGEMENT: 'INFRINGEMENT',
  OTHER: 'OTHER'
};

exports.ReportStatus = exports.$Enums.ReportStatus = {
  PENDING: 'PENDING',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED',
  ACTION_TAKEN: 'ACTION_TAKEN'
};

exports.ModerationActionType = exports.$Enums.ModerationActionType = {
  LOCK_THREAD: 'LOCK_THREAD',
  UNLOCK_THREAD: 'UNLOCK_THREAD',
  PIN_THREAD: 'PIN_THREAD',
  UNPIN_THREAD: 'UNPIN_THREAD',
  DELETE_THREAD_HARD: 'DELETE_THREAD_HARD',
  DELETE_POST_HARD: 'DELETE_POST_HARD'
};

exports.ProductCategory = exports.$Enums.ProductCategory = {
  IMAGE_PROCESSING: 'IMAGE_PROCESSING',
  DOCUMENT_TOOLS: 'DOCUMENT_TOOLS',
  PRODUCTIVITY: 'PRODUCTIVITY',
  DESIGN_TOOLS: 'DESIGN_TOOLS',
  DEVELOPER_TOOLS: 'DEVELOPER_TOOLS',
  MARKETING_TOOLS: 'MARKETING_TOOLS',
  OTHER: 'OTHER'
};

exports.ProductType = exports.$Enums.ProductType = {
  SERVICE: 'SERVICE',
  SOFTWARE: 'SOFTWARE',
  SUBSCRIPTION: 'SUBSCRIPTION',
  CREDIT_PACK: 'CREDIT_PACK',
  TEMPLATE: 'TEMPLATE'
};

exports.ProductStatus = exports.$Enums.ProductStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  COMING_SOON: 'COMING_SOON',
  DISCONTINUED: 'DISCONTINUED'
};

exports.PurchaseStatus = exports.$Enums.PurchaseStatus = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
  SUSPENDED: 'SUSPENDED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Notification: 'Notification',
  TileConfig: 'TileConfig',
  Embedding: 'Embedding',
  Plan: 'Plan',
  Subscription: 'Subscription',
  Invoice: 'Invoice',
  UserCredit: 'UserCredit',
  CreditLog: 'CreditLog',
  FeatureUsageLog: 'FeatureUsageLog',
  ForumCategory: 'ForumCategory',
  Follow: 'Follow',
  Bookmark: 'Bookmark',
  PointLog: 'PointLog',
  ForumThread: 'ForumThread',
  ForumPost: 'ForumPost',
  ForumReaction: 'ForumReaction',
  Report: 'Report',
  ModerationLog: 'ModerationLog',
  Setting: 'Setting',
  Product: 'Product',
  ProductPurchase: 'ProductPurchase',
  ProductUsageLog: 'ProductUsageLog',
  ProductReview: 'ProductReview',
  UserFile: 'UserFile',
  ProcessedFile: 'ProcessedFile'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
