import axios from 'axios';
import { useAuthStore } from '@/store/authStore'; // Import Pinia store
import { isTokenValid } from '@/utils/tokenUtils'; // Import token validation utility
import router from '@/router'; // Import router for redirection

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const registerUser = async (userData) => {
  try {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) { throw error.response.data; }
    throw error;
  }
};

export const loginUser = async (credentials) => {
  try {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) { throw error.response.data; }
    throw error;
  }
};

export const requestPasswordReset = async (email) => {
  try {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) { throw error.response.data; }
    throw error;
  }
};

export const resetPassword = async ({ token, newPassword }) => {
  try {
    const response = await apiClient.post('/auth/reset-password', { token, newPassword });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) { throw error.response.data; }
    throw error;
  }
};

export const verifyEmailRequest = async (token) => {
  try {
    const response = await apiClient.get(`/auth/verify-email/${token}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) { throw error.response.data; }
    throw error;
  }
};

export const setAuthHeader = (token) => {
  if (token) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete apiClient.defaults.headers.common['Authorization'];
  }
};

// Token refresh logic
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

export const refreshToken = async () => {
  const authStore = useAuthStore();
  const currentRefreshToken = authStore.getRefreshToken;

  if (!currentRefreshToken) {
    authStore.logoutAction();
    return Promise.reject(new Error('No refresh token available'));
  }

  try {
    const response = await apiClient.post('/auth/refresh-token', { refreshToken: currentRefreshToken });
    const { accessToken, refreshToken: newRefreshToken } = response.data;
    authStore.handleTokenRefresh({ accessToken, refreshToken: newRefreshToken });
    return accessToken;
  } catch (error) {
    authStore.logoutAction(); // Ensure redirect happens by default
    return Promise.reject(error);
  }
};

// Request interceptor to validate token before making requests
apiClient.interceptors.request.use(
  async config => {
    const authStore = useAuthStore();

    // Skip token validation for auth endpoints
    const isAuthEndpoint = config.url?.includes('/auth/') || config.url?.startsWith('/auth/');
    if (isAuthEndpoint) {
      console.log('Skipping token validation for auth endpoint:', config.url);
      return config;
    }

    // Check if we have an authorization header
    const authHeader = config.headers?.Authorization || apiClient.defaults.headers.common['Authorization'];
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '');

      // Validate token before making the request
      if (!isTokenValid(token)) {
        console.log('Token invalid before request, attempting refresh...');
        try {
          await authStore.ensureValidToken();
          // Token should be refreshed now, update the request header
          const newToken = authStore.getAccessToken;
          if (newToken) {
            config.headers.Authorization = `Bearer ${newToken}`;
          }
        } catch (error) {
          console.error('Failed to refresh token before request:', error);
          return Promise.reject(new Error('Authentication required'));
        }
      }
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling 401 errors
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;
    const authStore = useAuthStore();

    // Skip token refresh for auth endpoints
    const isAuthEndpoint = originalRequest.url?.includes('/auth/') || originalRequest.url?.startsWith('/auth/');
    if (isAuthEndpoint) {
      console.log('Skipping token refresh for auth endpoint error:', originalRequest.url);
      return Promise.reject(error);
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
        .then(token => {
          originalRequest.headers['Authorization'] = `Bearer ${token}`;
          return apiClient(originalRequest);
        })
        .catch(err => {
          return Promise.reject(err); // Propagate error from refreshToken
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newAccessToken = await refreshToken();
        processQueue(null, newAccessToken);
        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);
        // logoutAction is called within refreshToken on failure
        // but explicitly ensure router push if not already handled.
        if (router.currentRoute.value.name !== 'login') {
           router.push('/login');
        }
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    return Promise.reject(error);
  }
);

export default apiClient;
