generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

enum UserRole {
  ADMIN
  CUSTOMER
  EDITOR
  MODERATOR
}

enum ReportStatus {
  PENDING
  RESOLVED
  DISMISSED
  ACTION_TAKEN
}

enum ReportCategory {
  SPAM
  INAPPROPRIATE_CONTENT
  HATE_SPEECH
  INFRINGEMENT
  OTHER
}

enum ReactionEmoji {
  LIKE
  HEART
  LAUGH
  THINKING
  SAD
  ROCKET
}

enum PointActivityType {
  KB_ARTICLE_PUBLISHED
  KB_ARTICLE_HELPFUL_VOTE_RECEIVED
  KB_ARTICLE_VOTED_HELPFUL
  FORUM_THREAD_CREATED
  FORUM_COMMENT_CREATED
  FORUM_THREAD_SOLUTION_AWARDED
  FORUM_THREAD_MARKED_SOLVED
  FORUM_REACTION_RECEIVED
  FORUM_GAVE_REACTION
  MANUAL_ADJUSTMENT
}

enum ModerationActionType {
  LOCK_THREAD
  UNLOCK_THREAD
  PIN_THREAD
  UNPIN_THREAD
  DELETE_THREAD_HARD
  DELETE_POST_HARD
  // Future actions: EDIT_THREAD, DELETE_THREAD_SOFT, etc.
  // SET_REPORT_STATUS_PENDING (already used in service)
  // SET_REPORT_STATUS_RESOLVED (already used in service)
  // SET_REPORT_STATUS_DISMISSED (already used in service)
  // SET_REPORT_STATUS_ACTION_TAKEN (already used in service)
  // THREAD_DELETED_BY_MODERATOR (already used in service)
  // THREAD_DELETED_BY_AUTHOR_ADMIN (already used in service)
  // POST_DELETED_BY_MODERATOR (already used in service)
  // POST_DELETED_BY_AUTHOR_ADMIN (already used in service)
  // POST_UPDATED_IN_LOCKED_THREAD (already used in service)
  // ADMIN_POST_UPDATED_IN_LOCKED_THREAD (already used in service)
  // POST_MARKED_AS_SOLUTION_IN_LOCKED_THREAD (already used in service)
  // SOLUTION_CLEARED_IN_LOCKED_THREAD (already used in service)
}

model User {
  id                     String    @id @default(uuid())
  email                  String    @unique
  passwordHash           String
  name                   String?
  role                   UserRole  @default(CUSTOMER)
  isActive               Boolean   @default(true)
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt
  passwordResetToken     String?   @unique
  passwordResetExpiresAt DateTime?
  isEmailVerified        Boolean   @default(false)
  emailVerificationToken String?   @unique

  userCredit            UserCredit?
  currentSubscriptionId String?           @unique
  currentSubscription   Subscription?     @relation("UserCurrentSubscription", fields: [currentSubscriptionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subscriptions         Subscription[]    @relation("UserSubscriptions")
  creditLogs            CreditLog[]
  featureUsageLogs      FeatureUsageLog[]
  Invoice               Invoice[]

  forumThreads    ForumThread[]   @relation("UserThreads")
  forumPosts      ForumPost[]     @relation("UserPosts")
  forumReactions  ForumReaction[] @relation("UserReactions")
  reportsMade     Report[]        @relation("UserReportsMade")
  resolvedReports Report[]        @relation("ResolvedReports") // Reports this user resolved
  moderationLogs  ModerationLog[] @relation("ModeratorLogs") // Logs initiated by this user
  moderatedUsers  ModerationLog[] @relation("ModeratedUserLogs") // Logs where this user was the target

  userFollows   Follow[]   @relation("UserFollows")
  userBookmarks Bookmark[] @relation("UserBookmarks")
  userPointsLog PointLog[] @relation("UserPointsLog")
  totalPoints   Int        @default(0)

  notifications       Notification[] @relation("UserNotifications")
  notificationsCaused Notification[] @relation("NotificationActor")

  // Product/Service Relations
  productPurchases ProductPurchase[]
  productUsageLogs ProductUsageLog[]
  productReviews   ProductReview[]
  userFiles        UserFile[]
  processedFiles   ProcessedFile[]
}

model Notification {
  id        String   @id @default(uuid())
  userId    String // The user who should receive the notification
  user      User     @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
  type      String // Event type e.g., 'NEW_REPLY_YOUR_THREAD', 'POST_MARKED_SOLUTION'
  message   String // Human-readable message
  link      String? // Link to the relevant content (e.g., /forum/threads/slug#comment-id)
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  actorId   String? // User who triggered the event (e.g. who commented) - optional
  actor     User?    @relation("NotificationActor", fields: [actorId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([userId, isRead])
}

model TileConfig {
  id              String   @id @default(cuid())
  type            String   @unique
  name            String
  defaultData     Json?
  defaultPosition Json?
  isEnabled       Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model Embedding {
  id          String   @id @default(uuid())
  createdAt   DateTime @default(now())
  description String?
}

enum BillingCycle {
  MONTHLY
  YEARLY
  ONE_TIME
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  CANCELED
  EXPIRED
  PAST_DUE
}

enum InvoiceStatus {
  DRAFT
  ISSUED
  PAID
  VOID
  OVERDUE
  UNCOLLECTIBLE
}

enum PlanType {
  FREEMIUM
  SUBSCRIPTION_TIER_1
  SUBSCRIPTION_TIER_2
  CREDIT_PACK
}

enum CreditTransactionType {
  PURCHASE
  USAGE_EMBEDDING_CREATION
  USAGE_ADVANCED_SEARCH
  REFUND
  ADMIN_GRANT
  INITIAL_GRANT
}

enum FeatureType {
  EMBEDDING_CREATION
  ADVANCED_SEARCH
  DATA_EXPORT
  API_CALL
}

model Plan {
  id             String       @id @default(uuid())
  name           String       @unique
  description    String?
  priceCents     Int
  billingCycle   BillingCycle
  isActive       Boolean      @default(true)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  planType       PlanType     @default(SUBSCRIPTION_TIER_1)
  creditsGranted Int?
  featureQuotas  Json?

  subscriptions Subscription[]
  invoices      Invoice[]
}

model Subscription {
  id                String             @id @default(uuid())
  userId            String
  planId            String
  status            SubscriptionStatus @default(PENDING)
  startedAt         DateTime?
  expiresAt         DateTime?
  cancelAtPeriodEnd Boolean            @default(false)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt

  user        User      @relation("UserSubscriptions", fields: [userId], references: [id])
  plan        Plan      @relation(fields: [planId], references: [id])
  currentUser User?     @relation("UserCurrentSubscription")
  invoices    Invoice[]
}

model Invoice {
  id                   String        @id @default(uuid())
  userId               String
  subscriptionId       String?
  invoiceNumber        String        @unique
  totalAmountCents     Int
  taxAmountCents       Int           @default(0)
  invoiceDate          DateTime      @default(now())
  dueDate              DateTime?
  paidAt               DateTime?
  pdfPath              String?
  description          String?
  status               InvoiceStatus @default(DRAFT)
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  planId               String?
  creditPurchaseAmount Int?

  user         User          @relation(fields: [userId], references: [id])
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])
  plan         Plan?         @relation(fields: [planId], references: [id])
  creditLogs   CreditLog[]
}

model UserCredit {
  id        String   @id @default(uuid())
  userId    String   @unique
  balance   Int      @default(0)
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])
}

model CreditLog {
  id               String                @id @default(uuid())
  userId           String
  transactionType  CreditTransactionType
  amount           Int
  description      String?
  relatedInvoiceId String?
  createdAt        DateTime              @default(now())

  user           User     @relation(fields: [userId], references: [id])
  relatedInvoice Invoice? @relation(fields: [relatedInvoiceId], references: [id])
}

model FeatureUsageLog {
  id            String      @id @default(uuid())
  userId        String
  featureType   FeatureType
  unitsConsumed Int         @default(1)
  description   String?
  createdAt     DateTime    @default(now())

  user User @relation(fields: [userId], references: [id])
}

model ForumCategory {
  id          String        @id @default(uuid())
  name        String        @unique
  slug        String        @unique
  description String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  threads     ForumThread[]
  followers   Follow[]      @relation("ForumCategoryFollowers")
}

model Follow {
  id              String         @id @default(uuid())
  userId          String
  user            User           @relation("UserFollows", fields: [userId], references: [id])
  forumCategoryId String?
  forumCategory   ForumCategory? @relation("ForumCategoryFollowers", fields: [forumCategoryId], references: [id], onDelete: Cascade)
  createdAt       DateTime       @default(now())

  @@unique([userId, forumCategoryId])
  @@index([userId])
  @@index([forumCategoryId])
}

model Bookmark {
  id        String       @id @default(uuid())
  userId    String
  user      User         @relation("UserBookmarks", fields: [userId], references: [id])
  threadId  String?
  thread    ForumThread? @relation("ThreadBookmarks", fields: [threadId], references: [id], onDelete: Cascade)
  createdAt DateTime     @default(now())

  @@unique([userId, threadId])
  @@index([userId])
}

model PointLog {
  id              String            @id @default(uuid())
  userId          String
  user            User              @relation("UserPointsLog", fields: [userId], references: [id])
  points          Int
  activityType    PointActivityType
  description     String?
  relatedThreadId String?
  relatedThread   ForumThread?      @relation("ThreadPointEvents", fields: [relatedThreadId], references: [id], onDelete: SetNull)
  relatedPostId   String?
  relatedPost     ForumPost?        @relation("PostPointEvents", fields: [relatedPostId], references: [id], onDelete: SetNull)
  createdAt       DateTime          @default(now())

  @@index([userId])
  @@index([activityType])
  @@index([relatedThreadId])
  @@index([relatedPostId])
}

model ForumThread {
  id         String        @id @default(uuid())
  title      String
  slug       String        @unique
  content    String
  authorId   String
  author     User          @relation("UserThreads", fields: [authorId], references: [id])
  categoryId String
  category   ForumCategory @relation(fields: [categoryId], references: [id])
  viewCount  Int           @default(0)
  isPinned   Boolean       @default(false)
  isLocked   Boolean       @default(false)
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  tags       String? // JSON string for SQLite compatibility
  solutionId String?       @unique
  solution   ForumPost?    @relation("ThreadSolution", fields: [solutionId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  posts          ForumPost[]
  reactions      ForumReaction[] @relation("ThreadReactions")
  reports        Report[]        @relation("ThreadReports")
  moderationLogs ModerationLog[] @relation("ThreadModerationHistory")
  bookmarkedBy   Bookmark[]      @relation("ThreadBookmarks")
  pointEvents    PointLog[]      @relation("ThreadPointEvents")

  @@index([authorId])
  @@index([categoryId])
  @@index([isPinned, createdAt])
  @@index([slug])
}

model ForumPost {
  id           String      @id @default(uuid())
  content      String
  authorId     String
  author       User        @relation("UserPosts", fields: [authorId], references: [id])
  threadId     String
  thread       ForumThread @relation(fields: [threadId], references: [id], onDelete: Cascade) // Cascade delete if thread is deleted
  parentPostId String?
  parentPost   ForumPost?  @relation("Replies", fields: [parentPostId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies      ForumPost[] @relation("Replies")
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  solutionForThread ForumThread?    @relation("ThreadSolution")
  reactions         ForumReaction[] @relation("PostReactions")
  reports           Report[]        @relation("PostReports")
  moderationLogs    ModerationLog[] @relation("PostModerationHistory")
  pointEvents       PointLog[]      @relation("PostPointEvents")

  @@index([authorId])
  @@index([threadId])
  @@index([parentPostId])
}

model ForumReaction {
  id        String        @id @default(uuid())
  emoji     ReactionEmoji
  userId    String
  user      User          @relation("UserReactions", fields: [userId], references: [id])
  threadId  String?
  thread    ForumThread?  @relation("ThreadReactions", fields: [threadId], references: [id], onDelete: Cascade)
  postId    String?
  post      ForumPost?    @relation("PostReactions", fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  @@unique([userId, threadId, emoji], name: "userThreadReaction")
  @@unique([userId, postId, emoji], name: "userPostReaction")
  @@index([threadId])
  @@index([postId])
}

model Report {
  id              String          @id @default(uuid())
  reason          String
  category        ReportCategory // Added category field
  reporterId      String
  reporter        User            @relation("UserReportsMade", fields: [reporterId], references: [id], onDelete: Cascade)
  threadId        String?
  thread          ForumThread?    @relation("ThreadReports", fields: [threadId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  postId          String?
  post            ForumPost?      @relation("PostReports", fields: [postId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  status          ReportStatus    @default(PENDING)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  resolvedById    String?
  resolvedBy      User?           @relation("ResolvedReports", fields: [resolvedById], references: [id], onDelete: SetNull)
  resolvedAt      DateTime?
  resolutionNotes String?
  moderationLogs  ModerationLog[]
}

model ModerationLog {
  id              String               @id @default(uuid())
  action          ModerationActionType // Changed from String
  reason          String?
  moderatorId     String
  moderator       User                 @relation("ModeratorLogs", fields: [moderatorId], references: [id])
  targetUserId    String?
  targetUser      User?                @relation("ModeratedUserLogs", fields: [targetUserId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  targetThreadId  String?
  targetThread    ForumThread?         @relation("ThreadModerationHistory", fields: [targetThreadId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  targetPostId    String?
  targetPost      ForumPost?           @relation("PostModerationHistory", fields: [targetPostId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  relatedReportId String?
  relatedReport   Report?              @relation(fields: [relatedReportId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt
}

model Setting {
  key       String   @id
  value     Json?
  updatedAt DateTime @updatedAt
}

// Product/Service Management Models
model Product {
  id               String          @id @default(uuid())
  name             String
  slug             String          @unique
  description      String?
  shortDescription String?
  category         ProductCategory
  type             ProductType
  status           ProductStatus   @default(ACTIVE)

  // Pricing
  basePrice   Int // Price in cents
  currency    String       @default("USD")
  billingType BillingCycle @default(ONE_TIME)

  // Features & Limits
  features String? // JSON string of features
  limits   String? // JSON string of usage limits

  // Media
  imageUrl      String?
  galleryImages String? // JSON array of image URLs

  // SEO & Marketing
  metaTitle       String?
  metaDescription String?
  tags            String? // JSON array of tags

  // Configuration
  isDigital         Boolean @default(true)
  requiresSetup     Boolean @default(false)
  setupInstructions String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  purchases ProductPurchase[]
  usageLogs ProductUsageLog[]
  reviews   ProductReview[]
}

model ProductPurchase {
  id        String @id @default(uuid())
  userId    String
  productId String

  // Purchase Details
  purchasePrice Int // Price paid in cents
  currency      String         @default("USD")
  status        PurchaseStatus @default(PENDING)

  // Billing
  billingType   BillingCycle
  billingPeriod String? // For subscriptions: monthly, yearly
  expiresAt     DateTime? // For time-limited products

  // Usage Tracking
  usageCount Int       @default(0)
  usageLimit Int? // Null = unlimited
  lastUsedAt DateTime?

  // Activation
  activatedAt   DateTime?
  activationKey String?   @unique

  // Payment
  paymentId     String? // External payment processor ID
  paymentMethod String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user      User              @relation(fields: [userId], references: [id])
  product   Product           @relation(fields: [productId], references: [id])
  usageLogs ProductUsageLog[]

  @@index([userId])
  @@index([productId])
  @@index([status])
}

model ProductUsageLog {
  id         String @id @default(uuid())
  userId     String
  productId  String
  purchaseId String

  // Usage Details
  action     String // resize, watermark, convert, etc.
  inputData  String? // JSON of input parameters
  outputData String? // JSON of output results

  // File Information
  inputFileSize  Int? // Size in bytes
  outputFileSize Int? // Size in bytes
  inputFormat    String? // jpg, png, etc.
  outputFormat   String? // jpg, png, etc.

  // Processing Details
  processingTime Int? // Time in milliseconds
  success        Boolean @default(true)
  errorMessage   String?

  // Billing
  creditsUsed Int @default(1)

  createdAt DateTime @default(now())

  // Relations
  user     User            @relation(fields: [userId], references: [id])
  product  Product         @relation(fields: [productId], references: [id])
  purchase ProductPurchase @relation(fields: [purchaseId], references: [id])

  @@index([userId])
  @@index([productId])
  @@index([purchaseId])
  @@index([createdAt])
}

model ProductReview {
  id        String @id @default(uuid())
  userId    String
  productId String

  rating  Int // 1-5 stars
  title   String?
  content String?

  // Verification
  isVerified Boolean @default(false) // Verified purchase

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId]) // One review per user per product
  @@index([productId])
  @@index([rating])
}

// File Management for Image Processing
model UserFile {
  id     String @id @default(uuid())
  userId String

  // File Details
  originalName String
  fileName     String // Stored filename
  filePath     String // Storage path
  fileSize     Int // Size in bytes
  mimeType     String

  // Image Specific
  width  Int?
  height Int?
  format String? // jpg, png, gif, etc.

  // Processing
  isProcessed      Boolean @default(false)
  processingStatus String? // pending, processing, completed, failed

  // Organization
  folder String? // User-defined folder
  tags   String? // JSON array of tags

  // Access Control
  isPublic   Boolean @default(false)
  shareToken String? @unique

  // Metadata
  exifData String? // JSON of EXIF data

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user           User            @relation(fields: [userId], references: [id])
  processedFiles ProcessedFile[]

  @@index([userId])
  @@index([fileName])
  @@index([createdAt])
}

model ProcessedFile {
  id             String @id @default(uuid())
  originalFileId String
  userId         String

  // Processing Details
  operation  String // resize, watermark, convert, etc.
  parameters String // JSON of processing parameters

  // Output File
  fileName String
  filePath String
  fileSize Int
  mimeType String

  // Image Specific
  width  Int?
  height Int?
  format String?

  // Processing Info
  processingTime Int? // Time in milliseconds
  success        Boolean @default(true)
  errorMessage   String?

  createdAt DateTime @default(now())

  // Relations
  originalFile UserFile @relation(fields: [originalFileId], references: [id])
  user         User     @relation(fields: [userId], references: [id])

  @@index([originalFileId])
  @@index([userId])
  @@index([operation])
}

// Enums for Product System
enum ProductCategory {
  IMAGE_PROCESSING
  DOCUMENT_TOOLS
  PRODUCTIVITY
  DESIGN_TOOLS
  DEVELOPER_TOOLS
  MARKETING_TOOLS
  OTHER
}

enum ProductType {
  SERVICE // Pay-per-use service
  SOFTWARE // One-time software purchase
  SUBSCRIPTION // Recurring subscription
  CREDIT_PACK // Bundle of credits
  TEMPLATE // Digital template/asset
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  COMING_SOON
  DISCONTINUED
}

enum PurchaseStatus {
  PENDING
  ACTIVE
  EXPIRED
  CANCELLED
  REFUNDED
  SUSPENDED
}
