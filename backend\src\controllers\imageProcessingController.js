// imageController.js – refactored
// --------------------------------------------------------------
// Single‑responsibility helpers, centralised config & lean handlers
// --------------------------------------------------------------
import multer from 'multer'
import path from 'path'
import fs from 'fs/promises'
import { fileURLToPath } from 'url'

import imageProcessingService from '../services/imageProcessingService.js'
import fakeProductService from '../services/fakeProductService.js'
import logger from '../utils/logger.js'                    // winston / pino instance
import { BadRequestError, InternalServerError, UnauthorizedError } from '../utils/errors.js'
import * as fileValidation from '../utils/fileValidation.js'

// ---------------------------------------------------------------------------
// Constants & paths
// ---------------------------------------------------------------------------
const __filename  = fileURLToPath(import.meta.url)
const __dirname   = path.dirname(__filename)
const UPLOAD_DIR  = path.join(__dirname, '../../uploads')

// Configure once & export so tests can stub
export const uploadLimits = {
  fileSize      : 50 * 1024 * 1024,   // 50 MB
  files         : 1,
  fields        : 10,
  fieldSize     : 1 * 1024 * 1024,
  headerPairs   : 2000,
}

// ---------------------------------------------------------------------------
// Multer setup – disk for now (TODO: streaming pipeline)
// ---------------------------------------------------------------------------
const storage = multer.diskStorage({
  destination: async (_, __, cb) => {
    await fs.mkdir(UPLOAD_DIR, { recursive: true })
    cb(null, UPLOAD_DIR)
  },
  filename: (_, file, cb) => {
    const uid = `${Date.now()}-${Math.random().toString(36).slice(2)}`
    cb(null, `${file.fieldname}-${uid}${path.extname(file.originalname)}`)
  },
})

function fileFilter (_, file, cb) {
  try {
    // Skip size validation in fileFilter since size might not be available yet
    // We'll validate size after upload in the controller
    fileValidation.assertValidUpload(file, true) // true = skip size validation
    cb(null, true)
  } catch (err) {
    cb(err)
  }
}

export const upload = multer({ storage, fileFilter, limits: uploadLimits })

// ---------------------------------------------------------------------------
// Utility helpers
// ---------------------------------------------------------------------------
export async function cleanupFileWithRetry (filePath, max = 3, pause = 1_000) {
  for (let attempt = 1; attempt <= max; attempt++) {
    try {
      await fs.unlink(filePath)
      logger.debug({ filePath }, 'tmp‑file removed')
      return
    } catch (err) {
      if (attempt === max) {
        logger.warn({ filePath, err }, 'could not remove tmp‑file')
        return
      }
      await new Promise(r => setTimeout(r, pause))
    }
  }
}

// naive in‑memory rate‑limit (replace w/ Redis in prod)
const rlMap = new Map()
export function passRateLimit (userId, op) {
  const key = `${userId}:${op}`
  const now = Date.now()
  const windowMs = 60_000
  const maxReq   = 10

  const rec = rlMap.get(key) || { count: 0, reset: now + windowMs }
  if (now > rec.reset) {
    rlMap.set(key, { count: 1, reset: now + windowMs })
    return true
  }
  if (rec.count >= maxReq) return false
  rec.count++
  rlMap.set(key, rec)
  return true
}

async function validateProcessedFile (p) {
  const { size } = await fs.stat(p)
  if (size < 100)  throw new InternalServerError('processed file too small')
  if (size > 100 * 1024 * 1024) throw new InternalServerError('processed file too large')
}

async function authorisePurchase (userId) {
  const [purchase] = (await fakeProductService.getUserPurchases(userId, { status: 'ACTIVE' }))
    .filter(p => p.product?.category === 'IMAGE_PROCESSING')
  if (!purchase) throw new UnauthorizedError('no active image‑processing plan')
  if (purchase.usageLimit && purchase.usageCount >= purchase.usageLimit) {
    throw new UnauthorizedError('usage limit exceeded')
  }
  return purchase
}

// ---------------------------------------------------------------------------
// Controller factories – param validation in helpers to keep handlers tiny
// ---------------------------------------------------------------------------
function validateResizeBody ({ width, height, fit = 'cover', quality = 80 }) {
  if (!width && !height) throw new BadRequestError('width or height required')
  const max = 8_000, min = 1
  if (width  && (width  < min || width  > max)) throw new BadRequestError( `width ${width} invalid`)
  if (height && (height < min || height > max)) throw new BadRequestError( `height ${height} invalid`)
  const q = Number(quality)
  if (isNaN(q) || q < 1 || q > 100) throw new BadRequestError( 'quality 1‑100')
  return { width: +width || undefined, height: +height || undefined, fit, quality: q }
}

function validateWatermarkBody (b) {
  const { text, position = 'bottom-right', opacity = 0.7, fontSize = 24, color = 'white', fontFamily = 'Arial' } = b
  if (!text) throw new BadRequestError( 'watermark text required')
  if (text.length > 200) throw new BadRequestError( 'watermark text too long')
  const posOK = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center']
  if (!posOK.includes(position)) throw new BadRequestError( 'invalid position')
  const op = parseFloat(opacity)
  if (isNaN(op) || op < 0 || op > 1) throw new BadRequestError( 'opacity 0‑1')
  const fsz = parseInt(fontSize)
  if (isNaN(fsz) || fsz < 8 || fsz > 200) throw new BadRequestError( 'fontSize 8‑200')
  return { text, position, opacity: op, fontSize: fsz, color, fontFamily }
}

function validateConvertBody ({ format, quality = 80 }, originalFmt) {
  if (!format) throw new BadRequestError( 'target format required')
  const ok = ['jpg','jpeg','png','webp','gif','tiff']
  if (!ok.includes(format.toLowerCase())) throw new BadRequestError( 'unsupported format')
  const q = Number(quality)
  if (isNaN(q) || q < 1 || q > 100) throw new BadRequestError( 'quality 1‑100')
  if (format.toLowerCase() === originalFmt && Math.abs(q - 80) < 10) {
    throw new BadRequestError( 'conversion not worthwhile')
  }
  return { format, quality: q }
}

// ---------------------------------------------------------------------------
// Controllers
// ---------------------------------------------------------------------------
export const resizeImage = async (req, res, next) => {
  try {
    if (!req.file) throw new BadRequestError('no image upload')

    // Validate file size after upload
    try {
      fileValidation.assertValidUpload(req.file, false) // false = include size validation
    } catch (validationError) {
      await cleanupFileWithRetry(req.file.path) // Clean up uploaded file
      throw validationError
    }

    const { userId } = req.user
    if (!passRateLimit(userId, 'resize')) throw new BadRequestError('rate limit exceeded')

    const purchase = await authorisePurchase(userId)
    const params = validateResizeBody(req.body)

    const ext  = path.extname(req.file.originalname)
    const fmt  = req.body.format || ext.slice(1)
    const name = imageProcessingService.generateFileName(req.file.originalname, `_resized_${params.width||'auto'}x${params.height||'auto'}`).replace(ext, `.${fmt}`)
    const out  = path.join(imageProcessingService.processedDir, name)

    const result = await imageProcessingService.processImageWithTracking(userId, purchase.id, 'resize', req.file.path, out, params)
    if (!result.success) throw new InternalServerError( result.error || 'processing failed')

    await validateProcessedFile(out)
    await cleanupFileWithRetry(req.file.path)

    res.json({
      message : 'image resized',
      data    : {
        originalFile  : req.file.originalname,
        processedFile : name,
        downloadUrl   : `/downloads/${name}`,
        processingTime: result.processingTime,
        originalSize  : result.inputFileSize,
        processedSize : result.outputFileSize,
        compressionRatio: result.compressionRatio,
        remainingCredits: purchase.usageLimit
          ? purchase.usageLimit - purchase.usageCount - 1
          : 'unlimited'
      }
    })
  } catch (err) { next(err) }
}

export const addWatermark = async (req, res, next) => {
  try {
    if (!req.file) throw new BadRequestError( 'no image upload')

    // Validate file size after upload
    try {
      fileValidation.assertValidUpload(req.file, false) // false = include size validation
    } catch (validationError) {
      await cleanupFileWithRetry(req.file.path) // Clean up uploaded file
      throw validationError
    }

    const { userId } = req.user
    if (!passRateLimit(userId, 'watermark')) throw new BadRequestError( 'rate limit')

    const purchase = await authorisePurchase(userId)
    const params   = validateWatermarkBody(req.body)

    const name = imageProcessingService.generateFileName(req.file.originalname, '_watermarked')
    const out  = path.join(imageProcessingService.processedDir, name)

    const result = await imageProcessingService.processImageWithTracking(userId, purchase.id, 'watermark', req.file.path, out, params)
    if (!result.success) throw new InternalServerError( result.error || 'processing failed')

    await cleanupFileWithRetry(req.file.path)

    res.json({
      message: 'watermark added',
      data: {
        originalFile : req.file.originalname,
        processedFile: name,
        downloadUrl  : `/downloads/${name}`,
        processingTime: result.processingTime,
        originalSize : result.inputFileSize,
        processedSize: result.outputFileSize,
        watermarkApplied: result.watermarkApplied,
        remainingCredits: purchase.usageLimit
          ? purchase.usageLimit - purchase.usageCount - 1
          : 'unlimited'
      }
    })
  } catch (err) { next(err) }
}

export const convertFormat = async (req, res, next) => {
  try {
    if (!req.file) throw new BadRequestError( 'no image upload')

    // Validate file size after upload
    try {
      fileValidation.assertValidUpload(req.file, false) // false = include size validation
    } catch (validationError) {
      await cleanupFileWithRetry(req.file.path) // Clean up uploaded file
      throw validationError
    }

    const { userId } = req.user
    if (!passRateLimit(userId, 'convert')) throw new BadRequestError( 'rate limit')

    const purchase = await authorisePurchase(userId)
    const originalFmt = path.extname(req.file.originalname).slice(1).toLowerCase()
    const params = validateConvertBody(req.body, originalFmt)

    const name = imageProcessingService.generateFileName(req.file.originalname, '_converted').replace(path.extname(req.file.originalname), `.${params.format}`)
    const out  = path.join(imageProcessingService.processedDir, name)

    const result = await imageProcessingService.processImageWithTracking(userId, purchase.id, 'convert', req.file.path, out, { quality: params.quality })
    if (!result.success) throw new InternalServerError( result.error || 'processing failed')

    await cleanupFileWithRetry(req.file.path)

    res.json({
      message: 'image converted',
      data: {
        originalFile : req.file.originalname,
        processedFile: name,
        downloadUrl  : `/downloads/${name}`,
        processingTime: result.processingTime,
        originalSize : result.inputFileSize,
        processedSize: result.outputFileSize,
        originalFormat: originalFmt,
        convertedFormat: params.format,
        compressionRatio: result.compressionRatio,
        remainingCredits: purchase.usageLimit
          ? purchase.usageLimit - purchase.usageCount - 1
          : 'unlimited'
      }
    })
  } catch (err) { next(err) }
}

export const downloadImage = async (req, res, next) => {
  try {
    const { filename } = req.params
    if (!filename) throw new BadRequestError( 'filename required')
    if (/\.\.|[\/]/.test(filename)) throw new BadRequestError( 'directory traversal denied')
    if (!/^[\w-]+\.(?:jpe?g|png|webp|gif|tiff|bmp)$/.test(filename)) {
      throw new BadRequestError( 'invalid filename')
    }

    const filePath = path.join(imageProcessingService.processedDir, filename)
    const resolved = path.resolve(filePath)
    if (!resolved.startsWith(path.resolve(imageProcessingService.processedDir))) {
      throw new BadRequestError( 'access outside processedDir')
    }

    await fs.access(filePath)   // throws if not exist
    res.download(filePath, filename)
  } catch (err) { next(err) }
}

export const getImageMetadata = async (req, res, next) => {
  try {
    if (!req.file) throw new BadRequestError( 'no image upload')

    // Validate file size after upload
    try {
      fileValidation.assertValidUpload(req.file, false) // false = include size validation
    } catch (validationError) {
      await cleanupFileWithRetry(req.file.path) // Clean up uploaded file
      throw validationError
    }

    const { userId } = req.user
    if (!passRateLimit(userId, 'metadata')) throw new BadRequestError( 'rate limit')

    const meta = await imageProcessingService.getImageMetadata(req.file.path)
    await cleanupFileWithRetry(req.file.path)
    res.json({ message: 'metadata ok', data: { filename: req.file.originalname, ...meta } })
  } catch (err) { next(err) }
}

// ---------------------------------------------------------------------------
// Each controller relies on global error‑handler middleware to translate
// ApiError → status / json; uncaught errors default to 500.
// ---------------------------------------------------------------------------
